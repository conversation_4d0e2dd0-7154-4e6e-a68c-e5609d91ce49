<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>混合检索测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .weight-display {
            font-weight: bold;
            color: #0d6efd;
        }
        .source-card {
            border-left: 4px solid #0d6efd;
            background-color: #f8f9fa;
        }
        .score-badge {
            background: linear-gradient(45deg, #0d6efd, #6610f2);
        }
        .mode-badge {
            font-size: 0.9em;
        }
        .content-preview {
            max-height: 200px;
            overflow-y: auto;
            font-size: 0.9em;
            line-height: 1.4;
        }
        .loading-spinner {
            display: none;
        }
        .query-stats {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <!-- 左侧控制面板 -->
            <div class="col-md-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-search"></i> 混合检索测试</h5>
                    </div>
                    <div class="card-body">
                        <!-- 查询输入 -->
                        <div class="mb-3">
                            <label for="queryInput" class="form-label">查询问题</label>
                            <textarea id="queryInput" class="form-control" rows="3" 
                                placeholder="请输入您的问题...">什么是人工智能？</textarea>
                        </div>

                        <!-- 权重控制 -->
                        <div class="mb-3">
                            <label class="form-label">检索权重配置</label>
                            
                            <!-- BM25权重 -->
                            <div class="mb-2">
                                <label for="bm25Weight" class="form-label">
                                    BM25权重: <span id="bm25Display" class="weight-display">0.5</span>
                                </label>
                                <input type="range" class="form-range" id="bm25Weight" 
                                       min="0" max="1" step="0.1" value="0.5">
                            </div>

                            <!-- 向量权重 -->
                            <div class="mb-2">
                                <label for="vectorWeight" class="form-label">
                                    向量权重: <span id="vectorDisplay" class="weight-display">0.5</span>
                                </label>
                                <input type="range" class="form-range" id="vectorWeight" 
                                       min="0" max="1" step="0.1" value="0.5">
                            </div>

                            <!-- 检索模式显示 -->
                            <div class="text-center mt-2">
                                <span id="modeDisplay" class="badge bg-info mode-badge">混合检索</span>
                            </div>
                        </div>

                        <!-- 快速模式选择 -->
                        <div class="mb-3">
                            <label class="form-label">快速模式</label>
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setMode('vector')">
                                    纯向量
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setMode('hybrid')">
                                    混合
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setMode('bm25')">
                                    纯BM25
                                </button>
                            </div>
                        </div>

                        <!-- 其他参数 -->
                        <div class="mb-3">
                            <label for="maxResults" class="form-label">最大结果数</label>
                            <select id="maxResults" class="form-select">
                                <option value="3">3</option>
                                <option value="5" selected>5</option>
                                <option value="10">10</option>
                            </select>
                        </div>

                        <!-- 搜索按钮 -->
                        <button id="searchBtn" class="btn btn-primary w-100" onclick="performSearch()">
                            <i class="bi bi-search"></i> 开始检索
                        </button>

                        <!-- 加载状态 -->
                        <div id="loadingSpinner" class="loading-spinner text-center mt-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">检索中...</span>
                            </div>
                            <div class="mt-2">检索中，请稍候...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧结果面板 -->
            <div class="col-md-8">
                <div id="resultsContainer" class="d-none">
                    <!-- 查询统计信息 -->
                    <div class="card mb-3 query-stats">
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="h6 mb-1">检索模式</div>
                                    <span id="resultMode" class="badge bg-primary"></span>
                                </div>
                                <div class="col-md-3">
                                    <div class="h6 mb-1">处理时间</div>
                                    <span id="resultTime" class="fw-bold"></span>
                                </div>
                                <div class="col-md-3">
                                    <div class="h6 mb-1">源文档数</div>
                                    <span id="resultSources" class="fw-bold"></span>
                                </div>
                                <div class="col-md-3">
                                    <div class="h6 mb-1">权重配置</div>
                                    <small id="resultWeights" class="text-muted"></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AI回答 -->
                    <div class="card mb-3">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="bi bi-robot"></i> AI回答</h6>
                        </div>
                        <div class="card-body">
                            <div id="answerContent" class="lh-lg"></div>
                        </div>
                    </div>

                    <!-- 引用源文档 -->
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0"><i class="bi bi-file-text"></i> 引用源文档</h6>
                        </div>
                        <div class="card-body">
                            <div id="sourcesContainer"></div>
                        </div>
                    </div>
                </div>

                <!-- 错误信息 -->
                <div id="errorContainer" class="d-none">
                    <div class="alert alert-danger" role="alert">
                        <h6><i class="bi bi-exclamation-triangle"></i> 检索失败</h6>
                        <div id="errorMessage"></div>
                    </div>
                </div>

                <!-- 初始提示 -->
                <div id="initialPrompt" class="text-center text-muted py-5">
                    <i class="bi bi-search display-1 mb-3"></i>
                    <h4>混合检索测试工具</h4>
                    <p>调整左侧的权重参数，测试不同检索模式的效果</p>
                    <ul class="list-unstyled">
                        <li><strong>BM25检索</strong>：基于关键词匹配的稀疏检索</li>
                        <li><strong>向量检索</strong>：基于语义相似度的密集检索</li>
                        <li><strong>混合检索</strong>：结合两种方法的优势</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // API配置
        const API_BASE_URL = 'http://localhost:8000/api/v1';

        // 权重控制逻辑
        const bm25Slider = document.getElementById('bm25Weight');
        const vectorSlider = document.getElementById('vectorWeight');
        const bm25Display = document.getElementById('bm25Display');
        const vectorDisplay = document.getElementById('vectorDisplay');
        const modeDisplay = document.getElementById('modeDisplay');

        // 权重联动更新
        bm25Slider.addEventListener('input', function() {
            const bm25Value = parseFloat(this.value);
            const vectorValue = 1.0 - bm25Value;
            
            vectorSlider.value = vectorValue.toFixed(1);
            updateDisplays();
        });

        vectorSlider.addEventListener('input', function() {
            const vectorValue = parseFloat(this.value);
            const bm25Value = 1.0 - vectorValue;
            
            bm25Slider.value = bm25Value.toFixed(1);
            updateDisplays();
        });

        function updateDisplays() {
            const bm25Value = parseFloat(bm25Slider.value);
            const vectorValue = parseFloat(vectorSlider.value);
            
            bm25Display.textContent = bm25Value.toFixed(1);
            vectorDisplay.textContent = vectorValue.toFixed(1);
            
            // 更新模式显示
            let mode, modeClass;
            if (bm25Value === 0) {
                mode = '纯向量检索';
                modeClass = 'bg-success';
            } else if (vectorValue === 0) {
                mode = '纯BM25检索';
                modeClass = 'bg-warning';
            } else {
                mode = '混合检索';
                modeClass = 'bg-info';
            }
            
            modeDisplay.textContent = mode;
            modeDisplay.className = `badge mode-badge ${modeClass}`;
        }

        // 快速模式设置
        function setMode(mode) {
            switch(mode) {
                case 'vector':
                    bm25Slider.value = 0;
                    vectorSlider.value = 1;
                    break;
                case 'bm25':
                    bm25Slider.value = 1;
                    vectorSlider.value = 0;
                    break;
                case 'hybrid':
                    bm25Slider.value = 0.5;
                    vectorSlider.value = 0.5;
                    break;
            }
            updateDisplays();
        }

        // 执行搜索
        async function performSearch() {
            const query = document.getElementById('queryInput').value.trim();
            if (!query) {
                alert('请输入查询问题');
                return;
            }

            // 显示加载状态
            showLoading(true);
            hideResults();

            const requestData = {
                query: query,
                max_results: parseInt(document.getElementById('maxResults').value),
                bm25_weight: parseFloat(bm25Slider.value),
                vector_weight: parseFloat(vectorSlider.value)
            };

            try {
                const response = await fetch(`${API_BASE_URL}/query`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (response.ok) {
                    displayResults(result);
                } else {
                    displayError(result.detail || '检索失败');
                }

            } catch (error) {
                displayError(`网络错误: ${error.message}`);
            } finally {
                showLoading(false);
            }
        }

        // 显示结果
        function displayResults(result) {
            // 更新统计信息
            document.getElementById('resultMode').textContent = getModeText(result.retrieval_mode);
            document.getElementById('resultTime').textContent = `${result.processing_time.toFixed(3)}秒`;
            document.getElementById('resultSources').textContent = result.total_sources;
            
            const weights = result.weights_used;
            document.getElementById('resultWeights').textContent = 
                `BM25:${weights.bm25_weight} / 向量:${weights.vector_weight}`;

            // 显示AI回答
            document.getElementById('answerContent').innerHTML = formatAnswer(result.answer);

            // 显示源文档
            displaySources(result.sources);

            // 显示结果容器
            document.getElementById('resultsContainer').classList.remove('d-none');
            document.getElementById('initialPrompt').classList.add('d-none');
        }

        // 显示源文档
        function displaySources(sources) {
            const container = document.getElementById('sourcesContainer');
            
            if (!sources || sources.length === 0) {
                container.innerHTML = '<p class="text-muted">未找到相关源文档</p>';
                return;
            }

            let html = '';
            sources.forEach((source, index) => {
                const metadata = source.metadata || {};
                const score = source.score ? source.score.toFixed(3) : 'N/A';
                
                html += `
                    <div class="source-card p-3 mb-3 rounded">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0">
                                <i class="bi bi-file-text text-primary"></i>
                                源文档 ${index + 1}
                                ${metadata.title ? `- ${metadata.title}` : ''}
                            </h6>
                            <span class="badge score-badge text-white">评分: ${score}</span>
                        </div>
                        
                        ${metadata.filename ? `<small class="text-muted">文件: ${metadata.filename}</small><br>` : ''}
                        ${metadata.file_url ? `<small><a href="${metadata.file_url}" target="_blank" class="text-decoration-none">查看原文 <i class="bi bi-box-arrow-up-right"></i></a></small><br>` : ''}
                        
                        <div class="content-preview mt-2 p-2 bg-white rounded border">
                            <strong>内容预览：</strong><br>
                            ${formatContent(source.content)}
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // 格式化内容
        function formatContent(content) {
            if (!content) return '<em class="text-muted">无内容</em>';
            
            return content
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .substring(0, 500) + (content.length > 500 ? '...' : '');
        }

        // 格式化答案
        function formatAnswer(answer) {
            if (!answer) return '<em class="text-muted">无回答</em>';
            
            return answer
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        }

        // 获取模式文本
        function getModeText(mode) {
            const modeMap = {
                'hybrid': '混合检索',
                'vector_only': '纯向量检索',
                'bm25_only': '纯BM25检索',
                'vector_fallback': '向量回退',
                'error': '检索失败'
            };
            return modeMap[mode] || mode;
        }

        // 显示错误
        function displayError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorContainer').classList.remove('d-none');
            document.getElementById('resultsContainer').classList.add('d-none');
        }

        // 隐藏结果
        function hideResults() {
            document.getElementById('resultsContainer').classList.add('d-none');
            document.getElementById('errorContainer').classList.add('d-none');
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            const spinner = document.getElementById('loadingSpinner');
            const button = document.getElementById('searchBtn');
            
            if (show) {
                spinner.style.display = 'block';
                button.disabled = true;
                button.innerHTML = '<i class="bi bi-hourglass-split"></i> 检索中...';
            } else {
                spinner.style.display = 'none';
                button.disabled = false;
                button.innerHTML = '<i class="bi bi-search"></i> 开始检索';
            }
        }

        // 初始化
        updateDisplays();
    </script>
</body>
</html>
